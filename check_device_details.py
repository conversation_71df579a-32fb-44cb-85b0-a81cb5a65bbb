#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Virtual Audio Driver设备详细信息
"""

import sounddevice as sd

def check_device_details():
    """检查设备详细信息"""
    print("🔍 检查Virtual Audio Driver设备详细信息")
    print("=" * 80)
    
    devices = sd.query_devices()
    
    # 找到所有虚拟设备
    virtual_devices = []
    for i, device in enumerate(devices):
        name = device['name'].lower()
        if 'virtual' in name:
            virtual_devices.append((i, device))
    
    print("📋 Virtual Audio Driver设备详细信息:")
    print("=" * 80)
    
    for device_id, device in virtual_devices:
        print(f"\n设备 {device_id}: {device['name']}")
        print(f"   输入声道: {device['max_input_channels']}")
        print(f"   输出声道: {device['max_output_channels']}")
        print(f"   默认采样率: {device['default_samplerate']}")
        print(f"   低延迟: {device['default_low_input_latency']:.4f}s / {device['default_low_output_latency']:.4f}s")
        print(f"   高延迟: {device['default_high_input_latency']:.4f}s / {device['default_high_output_latency']:.4f}s")
        
        # 获取主机API信息
        try:
            hostapi = sd.query_hostapis(device['hostapi'])
            print(f"   主机API: {hostapi['name']}")
        except:
            print(f"   主机API: 未知")
        
        # 判断设备状态
        if device['max_input_channels'] > 0 and device['max_output_channels'] > 0:
            device_type = "双向设备 (输入+输出)"
        elif device['max_input_channels'] > 0:
            device_type = "输入设备 (麦克风)"
        elif device['max_output_channels'] > 0:
            device_type = "输出设备 (扬声器)"
        else:
            device_type = "未知设备"
        
        print(f"   设备类型: {device_type}")
        
        # 检查设备是否可用
        try:
            # 尝试查询设备支持的采样率
            supported_rates = []
            test_rates = [8000, 11025, 16000, 22050, 44100, 48000, 96000]
            
            for rate in test_rates:
                try:
                    if device['max_output_channels'] > 0:
                        sd.check_output_settings(device=device_id, samplerate=rate)
                        supported_rates.append(rate)
                    elif device['max_input_channels'] > 0:
                        sd.check_input_settings(device=device_id, samplerate=rate)
                        supported_rates.append(rate)
                except:
                    pass
            
            if supported_rates:
                print(f"   支持采样率: {supported_rates}")
                print(f"   设备状态: ✅ 可用")
            else:
                print(f"   设备状态: ❌ 不可用或有问题")
                
        except Exception as e:
            print(f"   设备状态: ❌ 检查失败 - {e}")
    
    print("\n" + "=" * 80)
    print("🎯 建议:")
    print("1. 使用状态为'✅ 可用'的设备")
    print("2. 确保扬声器和麦克风支持相同的采样率")
    print("3. 优先使用支持44100Hz采样率的设备")
    
    # 检查默认设备
    try:
        default_input = sd.default.device[0]
        default_output = sd.default.device[1]
        print(f"\n🎯 当前默认设备:")
        print(f"   默认输入设备: {default_input} - {devices[default_input]['name']}")
        print(f"   默认输出设备: {default_output} - {devices[default_output]['name']}")
    except:
        print(f"\n❌ 无法获取默认设备信息")

if __name__ == "__main__":
    try:
        check_device_details()
    except Exception as e:
        print(f"❌ 检查失败: {e}")
    
    input("\n按Enter键退出...")
