#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查实际的Virtual Audio Driver设备名称
"""

import sounddevice as sd

def check_device_names():
    """检查实际的设备名称"""
    print("🔍 检查Virtual Audio Driver实际设备名称")
    print("=" * 60)
    
    try:
        devices = sd.query_devices()
        
        print("📋 所有音频设备:")
        virtual_devices = []
        
        for i, device in enumerate(devices):
            name = device['name']
            input_ch = device['max_input_channels']
            output_ch = device['max_output_channels']
            
            # 检查是否包含virtual关键词
            if 'virtual' in name.lower():
                virtual_devices.append((i, name, input_ch, output_ch))
                print(f"🎯 {i:2d}: {name}")
                print(f"     输入声道: {input_ch}, 输出声道: {output_ch}")
            else:
                print(f"   {i:2d}: {name}")
        
        print(f"\n📊 Virtual设备分析:")
        if virtual_devices:
            for i, name, input_ch, output_ch in virtual_devices:
                print(f"\n设备 {i}: {name}")
                
                # 分析设备类型
                if output_ch > 0 and input_ch == 0:
                    print(f"   类型: 虚拟扬声器 (只输出)")
                elif input_ch > 0 and output_ch == 0:
                    print(f"   类型: 虚拟麦克风 (只输入)")
                elif input_ch > 0 and output_ch > 0:
                    print(f"   类型: 虚拟音频设备 (输入+输出)")
                else:
                    print(f"   类型: 未知")
                
                # 检查名称模式
                name_lower = name.lower()
                if 'virtual audio driver' in name_lower:
                    print(f"   匹配: Virtual Audio Driver ✅")
                elif 'virtual mic driver' in name_lower:
                    print(f"   匹配: Virtual Mic Driver ✅")
                elif 'virtual' in name_lower:
                    print(f"   匹配: 包含Virtual关键词 ⚠️")
        else:
            print("❌ 未找到任何Virtual设备")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    check_device_names()
