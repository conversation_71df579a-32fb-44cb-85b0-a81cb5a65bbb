#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试Virtual Audio Driver设备配对
"""

import sounddevice as sd
import numpy as np
import time

def simple_test():
    """简单的配对测试"""
    print("🔍 简单Virtual Audio Driver配对测试")
    print("=" * 60)
    
    # 根据之前的检查结果，测试最有可能的配对
    test_pairs = [
        (4, 1, "扬声器 (Virtual Audio Driver by MT) → 麦克风阵列 (Virtual Audio Driver by)"),
        (10, 7, "扬声器 (Virtual Audio Driver by MTT) → 麦克风阵列 (Virtual Audio Driver by MTT)"),
    ]
    
    # 生成测试音频
    sample_rate = 44100
    duration = 2.0
    frequency = 440
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    test_audio = np.sin(2 * np.pi * frequency * t) * 0.2
    test_audio = np.column_stack([test_audio, test_audio])
    
    print(f"🎵 测试音频: {frequency}Hz 正弦波, {duration}秒")
    print("=" * 60)
    
    for speaker_id, mic_id, description in test_pairs:
        print(f"\n🔊 测试配对: {description}")
        print(f"   扬声器ID: {speaker_id}, 麦克风ID: {mic_id}")
        
        try:
            # 录音数据收集
            recorded_data = []
            
            def record_callback(indata, frames, time, status):
                recorded_data.extend(indata[:, 0])  # 左声道
            
            # 开始录音和播放
            print("   🎤 开始录音...")
            with sd.InputStream(device=mic_id, callback=record_callback, 
                              samplerate=sample_rate, channels=2):
                time.sleep(0.2)  # 等待录音稳定
                
                print("   🔊 播放测试音频...")
                sd.play(test_audio, samplerate=sample_rate, device=speaker_id)
                sd.wait()  # 等待播放完成
                
                time.sleep(0.2)  # 额外录音时间
            
            # 分析结果
            if len(recorded_data) > 0:
                recorded_array = np.array(recorded_data)
                max_amplitude = np.max(np.abs(recorded_array))
                mean_amplitude = np.mean(np.abs(recorded_data))
                
                print(f"   📊 录音分析:")
                print(f"      样本数量: {len(recorded_data)}")
                print(f"      最大振幅: {max_amplitude:.4f}")
                print(f"      平均振幅: {mean_amplitude:.4f}")
                
                if max_amplitude > 0.01:
                    print(f"   ✅ 成功! 这是一个有效的配对!")
                    print(f"   🎯 建议在程序中使用这个配对")
                else:
                    print(f"   ❌ 失败: 未检测到足够的音频信号")
            else:
                print(f"   ❌ 失败: 无录音数据")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
        
        time.sleep(1)  # 设备间隔
    
    print("\n" + "=" * 60)
    print("🎯 测试完成!")
    print("✅ 标记为'成功'的配对可以用于虚拟麦克风")
    print("💡 在主程序中选择成功的设备配对")

if __name__ == "__main__":
    try:
        simple_test()
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
    
    input("\n按Enter键退出...")
