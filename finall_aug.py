#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MP3虚拟麦克风 - 终极完美版本
结合了模块化架构、多音频引擎、现代化GUI和完整功能的最终解决方案

作者: Augment Agent
版本: v2.0 Final
日期: 2025-08-02
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import time
import os
import sys
import platform
import subprocess
import queue
import numpy as np
from pathlib import Path
from typing import List, Dict, Optional, Callable

# 音频处理库导入
try:
    import sounddevice as sd
    SOUNDDEVICE_AVAILABLE = True
except ImportError:
    SOUNDDEVICE_AVAILABLE = False

try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False

class AudioDeviceManager:
    """高级音频设备管理器 - 跨平台设备检测和管理"""
    
    def __init__(self):
        self.system = platform.system()
        self.devices = []
        self.virtual_devices = []
        
    def get_all_devices(self) -> List[Dict]:
        """获取所有音频设备信息"""
        devices = []
        
        # 使用sounddevice获取设备
        if SOUNDDEVICE_AVAILABLE:
            devices.extend(self._get_devices_sounddevice())
        else:
            devices.extend(self._get_devices_system())
            
        return self._filter_and_categorize_devices(devices)
    
    def _get_devices_sounddevice(self) -> List[Dict]:
        """使用sounddevice获取设备"""
        devices = []
        try:
            device_list = sd.query_devices()
            for i, device in enumerate(device_list):
                devices.append({
                    'id': i,
                    'name': device['name'],
                    'max_input_channels': device['max_input_channels'],
                    'max_output_channels': device['max_output_channels'],
                    'default_samplerate': device['default_samplerate'],
                    'hostapi': sd.query_hostapis(device['hostapi'])['name'],
                    'type': self._determine_device_type(device['name']),
                    'api': 'sounddevice'
                })
        except Exception as e:
            print(f"SoundDevice设备获取失败: {e}")
        return devices
    
    def _get_devices_pyaudio(self) -> List[Dict]:
        """PyAudio设备获取 - 当前版本不支持"""
        return []
    
    def _get_devices_system(self) -> List[Dict]:
        """使用系统命令获取设备"""
        devices = []
        if self.system == "Windows":
            devices.extend(self._get_windows_devices())
        elif self.system == "Darwin":
            devices.extend(self._get_macos_devices())
        elif self.system == "Linux":
            devices.extend(self._get_linux_devices())
        return devices
    
    def _get_windows_devices(self) -> List[Dict]:
        """获取Windows设备"""
        devices = []
        try:
            cmd = ["powershell", "-Command", 
                   "Get-WmiObject -Class Win32_SoundDevice | Select-Object Name | Format-Table -HideTableHeaders"]
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for i, line in enumerate(lines):
                    device_name = line.strip()
                    if device_name and device_name != "Name":
                        devices.append({
                            'id': i,
                            'name': device_name,
                            'max_input_channels': 2,
                            'max_output_channels': 2,
                            'default_samplerate': 44100,
                            'hostapi': 'Windows',
                            'type': self._determine_device_type(device_name),
                            'api': 'system'
                        })
        except Exception as e:
            print(f"Windows设备获取失败: {e}")
        return devices
    
    def _get_macos_devices(self) -> List[Dict]:
        """获取macOS设备 - 当前版本不支持"""
        return []

    def _get_linux_devices(self) -> List[Dict]:
        """获取Linux设备 - 当前版本不支持"""
        return []
    
    def _determine_device_type(self, device_name: str) -> str:
        """判断设备类型"""
        name_lower = device_name.lower()
        
        # 虚拟设备检测
        virtual_keywords = ['virtual', 'cable', 'voicemeeter', 'vb-audio', 'obs', 'loopback']
        if any(keyword in name_lower for keyword in virtual_keywords):
            return 'virtual'
        
        # 物理设备类型
        if any(keyword in name_lower for keyword in ['microphone', 'mic', 'input']):
            return 'microphone'
        elif any(keyword in name_lower for keyword in ['speaker', 'headphone', 'output']):
            return 'speaker'
        else:
            return 'unknown'
    
    def _filter_and_categorize_devices(self, devices: List[Dict]) -> Dict[str, List[Dict]]:
        """过滤和分类设备"""
        categorized = {
            'virtual_speakers': [],
            'virtual_microphones': [],
            'physical_speakers': [],
            'physical_microphones': [],
            'other': [],
            'device_pairs': []  # 添加设备配对信息
        }

        seen_names = set()
        for device in devices:
            name = device['name']
            if name in seen_names:
                continue
            seen_names.add(name)

            device_type = device['type']
            has_input = device['max_input_channels'] > 0
            has_output = device['max_output_channels'] > 0

            if device_type == 'virtual':
                if has_output:
                    categorized['virtual_speakers'].append(device)
                if has_input:
                    categorized['virtual_microphones'].append(device)
            elif device_type in ['speaker', 'unknown'] and has_output:
                categorized['physical_speakers'].append(device)
            elif device_type == 'microphone' and has_input:
                categorized['physical_microphones'].append(device)
            else:
                categorized['other'].append(device)

        # 检测设备配对
        categorized['device_pairs'] = self._detect_device_pairs(
            categorized['virtual_speakers'],
            categorized['virtual_microphones']
        )

        return categorized

    def _detect_device_pairs(self, speakers: List[Dict], microphones: List[Dict]) -> List[Dict]:
        """检测虚拟音频设备配对"""
        pairs = []

        for speaker in speakers:
            speaker_name = speaker['name'].lower()
            speaker_rate = speaker['default_samplerate']

            for mic in microphones:
                mic_name = mic['name'].lower()
                mic_rate = mic['default_samplerate']

                # 检查名称相似性和采样率匹配
                similarity_score = 0

                # 名称匹配检查
                if 'virtual audio driver' in speaker_name and 'virtual audio driver' in mic_name:
                    # 提取厂商标识
                    speaker_vendor = speaker_name.split('by')[-1].strip() if 'by' in speaker_name else ''
                    mic_vendor = mic_name.split('by')[-1].strip() if 'by' in mic_name else ''

                    if speaker_vendor and mic_vendor and speaker_vendor == mic_vendor:
                        similarity_score += 50  # 厂商匹配
                    elif speaker_vendor and mic_vendor:
                        # 部分匹配（如 "MT" 和 "MTT"）
                        if speaker_vendor in mic_vendor or mic_vendor in speaker_vendor:
                            similarity_score += 30

                # 采样率匹配
                if abs(speaker_rate - mic_rate) < 100:  # 允许小误差
                    similarity_score += 30

                # API匹配
                if speaker.get('hostapi') == mic.get('hostapi'):
                    similarity_score += 20

                if similarity_score >= 50:  # 阈值
                    pairs.append({
                        'speaker': speaker,
                        'microphone': mic,
                        'similarity_score': similarity_score,
                        'sample_rate': int(min(speaker_rate, mic_rate)),
                        'recommended': similarity_score >= 80
                    })

        # 按相似度排序
        pairs.sort(key=lambda x: x['similarity_score'], reverse=True)
        return pairs

class AudioProcessor:
    """高级音频处理器 - 多引擎支持和智能回退"""
    
    def __init__(self):
        self.current_audio = None
        self.is_playing = False
        self.is_paused = False
        self.volume = 0.7
        self.loop_enabled = True
        self.current_position = 0
        self.audio_length = 0
        self.playback_thread = None
        self.output_device_id = None
        self.sample_rate = 44100  # 默认采样率

        # 初始化音频引擎
        self.engines = self._initialize_engines()
        self.current_engine = self._select_best_engine()
        
    def _initialize_engines(self) -> Dict[str, bool]:
        """初始化音频引擎 - 当前版本只支持SoundDevice"""
        engines = {}

        # SoundDevice引擎
        engines['sounddevice'] = SOUNDDEVICE_AVAILABLE

        # 其他引擎标记为不可用
        engines['pygame'] = False
        engines['pyaudio'] = False

        return engines
    
    def _select_best_engine(self) -> str:
        """选择最佳音频引擎 - 当前版本只支持SoundDevice"""
        if self.engines.get('sounddevice'):
            return 'sounddevice'
        else:
            return None
    
    def load_mp3_file(self, file_path: str, target_sample_rate: int = 44100) -> bool:
        """加载MP3文件"""
        try:
            if not PYDUB_AVAILABLE:
                raise Exception("pydub库未安装，无法加载MP3文件")

            # 使用pydub加载MP3
            audio = AudioSegment.from_mp3(file_path)

            # 标准化音频格式 - 支持不同采样率
            audio = audio.set_frame_rate(target_sample_rate)
            audio = audio.set_channels(2)
            audio = audio.set_sample_width(2)

            # 转换为numpy数组（用于sounddevice）
            samples = np.array(audio.get_array_of_samples(), dtype=np.float32)
            samples = samples.reshape((-1, 2))
            samples = samples / 32768.0  # 归一化

            self.current_audio = samples
            self.audio_length = len(samples) / target_sample_rate
            self.current_position = 0
            self.sample_rate = target_sample_rate  # 保存采样率

            return True

        except Exception as e:
            raise Exception(f"加载MP3文件失败: {str(e)}")
    
    def start_playback(self, device_id: int = None) -> bool:
        """开始播放"""
        if self.current_audio is None:
            return False
        
        if self.current_engine is None:
            raise Exception("没有可用的音频引擎")
        
        self.output_device_id = device_id
        self.is_playing = True
        self.is_paused = False
        
        # 启动播放线程
        self.playback_thread = threading.Thread(target=self._playback_loop, daemon=True)
        self.playback_thread.start()
        
        return True
    
    def _playback_loop(self):
        """播放循环 - 使用SoundDevice引擎"""
        try:
            if self.current_engine == 'sounddevice':
                self._playback_sounddevice()
            else:
                raise Exception("不支持的音频引擎")
        except Exception as e:
            print(f"播放错误: {e}")
        finally:
            self.is_playing = False
    
    def _playback_sounddevice(self):
        """使用SoundDevice播放 - 优化版本"""
        sample_rate = self.sample_rate  # 使用动态采样率
        chunk_samples = sample_rate // 10  # 约100ms的小块，减少卡顿

        # 预先计算音量调整后的音频数据
        current_volume = self.volume
        audio_data = self.current_audio * current_volume

        while self.is_playing:
            if self.is_paused:
                time.sleep(0.01)  # 暂停时短暂休眠
                continue

            # 检查音量是否改变，如果改变则重新计算
            if abs(self.volume - current_volume) > 0.01:
                current_volume = self.volume
                audio_data = self.current_audio * current_volume

            # 获取当前播放位置的音频块
            start_sample = int(self.current_position * sample_rate)
            if start_sample >= len(audio_data):
                if self.loop_enabled:
                    self.current_position = 0
                    continue
                else:
                    break

            end_sample = min(start_sample + chunk_samples, len(audio_data))
            chunk = audio_data[start_sample:end_sample]

            if len(chunk) > 0:
                try:
                    # 使用非阻塞播放
                    sd.play(chunk, samplerate=sample_rate, device=self.output_device_id)

                    # 精确的时间控制，避免使用sd.wait()
                    chunk_duration = len(chunk) / sample_rate
                    time.sleep(chunk_duration * 0.98)  # 稍微提前一点，确保连续性

                    # 更新位置
                    self.current_position += chunk_duration

                except Exception as e:
                    print(f"音频播放错误: {e}")
                    time.sleep(0.01)  # 短暂延迟后继续
    
    def _playback_pygame(self):
        """使用Pygame播放 - 当前版本不支持"""
        raise NotImplementedError("Pygame播放引擎尚未实现")

    def _playback_pyaudio(self):
        """使用PyAudio播放 - 当前版本不支持"""
        raise NotImplementedError("PyAudio播放引擎尚未实现")
    
    def pause(self):
        """暂停播放"""
        self.is_paused = True
    
    def resume(self):
        """恢复播放"""
        self.is_paused = False
    
    def stop(self):
        """停止播放"""
        self.is_playing = False
        self.is_paused = False
        self.current_position = 0

        try:
            if self.current_engine == 'sounddevice':
                sd.stop()
        except Exception as e:
            print(f"停止播放时出错: {e}")
    
    def set_volume(self, volume: float):
        """设置音量"""
        self.volume = max(0.0, min(1.0, volume))
    
    def set_loop(self, enabled: bool):
        """设置循环播放"""
        self.loop_enabled = enabled
    
    def get_position(self) -> float:
        """获取当前播放位置"""
        return self.current_position
    
    def get_duration(self) -> float:
        """获取音频总时长"""
        return self.audio_length

class VirtualMicrophoneGUI:
    """现代化虚拟麦克风GUI - 终极版本"""

    def __init__(self, root):
        self.root = root
        self.setup_window()

        # 核心组件
        self.device_manager = AudioDeviceManager()
        self.audio_processor = AudioProcessor()

        # 状态变量
        self.current_file = None
        self.is_playing = False
        self.devices_info = {}

        # 麦克风监控
        self.is_monitoring_mic = False
        self.mic_monitor_thread = None
        self.mic_activity_data = {'max_amplitude': 0, 'activity_rate': 0}

        # UI状态
        self.compact_mode = False

        # GUI更新队列
        self.update_queue = queue.Queue()

        # 创建界面
        self.create_widgets()
        self.refresh_devices()
        self.start_update_loop()

    def setup_window(self):
        """设置主窗口"""
        self.root.title("🎤 MP3虚拟麦克风 - 终极版 v2.0")
        self.root.geometry("950x700")  # 稍微调整尺寸
        self.root.resizable(True, True)
        self.root.configure(bg='#f0f0f0')

        # 设置现代化样式
        style = ttk.Style()
        available_themes = style.theme_names()
        if 'vista' in available_themes:
            style.theme_use('vista')
        elif 'clam' in available_themes:
            style.theme_use('clam')

        # 自定义样式
        style.configure('Title.TLabel', font=('Arial', 18, 'bold'), background='#f0f0f0')
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'), background='#f0f0f0')
        style.configure('Status.TLabel', font=('Arial', 10), background='#f0f0f0')
        style.configure('Success.TLabel', font=('Arial', 10), background='#f0f0f0', foreground='#28a745')
        style.configure('Error.TLabel', font=('Arial', 10), background='#f0f0f0', foreground='#dc3545')

        # 滚动条样式
        style.configure('Vertical.TScrollbar', background='#e0e0e0', troughcolor='#f0f0f0',
                       borderwidth=1, arrowcolor='#666666')

    def create_widgets(self):
        """创建所有GUI组件"""
        # 配置根窗口网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

        # 创建主容器框架
        main_container = ttk.Frame(self.root)
        main_container.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=10)
        main_container.columnconfigure(0, weight=1)
        main_container.rowconfigure(0, weight=1)

        # 创建Canvas和滚动条
        self.canvas = tk.Canvas(main_container, bg='#f0f0f0', highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(main_container, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)

        # 配置滚动
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        # 布局Canvas和滚动条
        self.canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 配置滚动框架
        self.scrollable_frame.columnconfigure(0, weight=1)

        # 绑定鼠标滚轮事件
        self.bind_mousewheel()

        # 创建内容区域
        content_frame = ttk.Frame(self.scrollable_frame, padding="10")
        content_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        content_frame.columnconfigure(0, weight=1)

        # 标题和状态
        self.create_header(content_frame, 0)

        # 文件选择区域
        self.create_file_section(content_frame, 1)

        # 设备选择区域
        self.create_device_section(content_frame, 2)

        # 播放控制区域
        self.create_control_section(content_frame, 3)

        # 高级设置区域
        self.create_advanced_section(content_frame, 4)

        # 状态监控区域
        self.create_status_section(content_frame, 5)

        # 设备详情区域
        self.create_device_details_section(content_frame, 6)

    def bind_mousewheel(self):
        """绑定鼠标滚轮事件"""
        def _on_mousewheel(event):
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_to_mousewheel(event):
            self.canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_from_mousewheel(event):
            self.canvas.unbind_all("<MouseWheel>")

        # 绑定鼠标进入和离开事件
        self.canvas.bind('<Enter>', _bind_to_mousewheel)
        self.canvas.bind('<Leave>', _unbind_from_mousewheel)

    def create_header(self, parent, row):
        """创建标题和状态区域"""
        header_frame = ttk.Frame(parent)
        header_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        header_frame.columnconfigure(1, weight=1)

        # 主标题
        title_label = ttk.Label(header_frame, text="🎤 MP3虚拟麦克风 - 终极版", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

        # 系统状态
        status_frame = ttk.Frame(header_frame)
        status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E))
        status_frame.columnconfigure((0, 1, 2, 3), weight=1)

        # 音频引擎状态
        ttk.Label(status_frame, text="音频引擎:", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W)
        self.engine_status_var = tk.StringVar(value="检测中...")
        ttk.Label(status_frame, textvariable=self.engine_status_var, style='Status.TLabel').grid(row=1, column=0, sticky=tk.W)

        # 虚拟设备状态
        ttk.Label(status_frame, text="虚拟设备:", style='Heading.TLabel').grid(row=0, column=1, sticky=tk.W)
        self.virtual_status_var = tk.StringVar(value="检测中...")
        ttk.Label(status_frame, textvariable=self.virtual_status_var, style='Status.TLabel').grid(row=1, column=1, sticky=tk.W)

        # 播放状态
        ttk.Label(status_frame, text="播放状态:", style='Heading.TLabel').grid(row=0, column=2, sticky=tk.W)
        self.play_status_var = tk.StringVar(value="停止")
        ttk.Label(status_frame, textvariable=self.play_status_var, style='Status.TLabel').grid(row=1, column=2, sticky=tk.W)

        # 系统信息
        ttk.Label(status_frame, text="系统:", style='Heading.TLabel').grid(row=0, column=3, sticky=tk.W)
        system_info = f"{platform.system()} {platform.release()}"
        ttk.Label(status_frame, text=system_info, style='Status.TLabel').grid(row=1, column=3, sticky=tk.W)

        # 添加紧凑模式切换按钮
        compact_frame = ttk.Frame(header_frame)
        compact_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))

        self.compact_btn = ttk.Button(compact_frame, text="📱 紧凑模式", command=self.toggle_compact_mode)
        self.compact_btn.grid(row=0, column=0)

    def create_file_section(self, parent, row):
        """创建文件选择区域"""
        file_frame = ttk.LabelFrame(parent, text="📁 音频文件管理", padding="10")
        file_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)

        # 文件选择
        ttk.Label(file_frame, text="MP3文件:", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        self.file_path_var = tk.StringVar(value="未选择文件")
        self.file_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, state='readonly')
        self.file_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        self.browse_btn = ttk.Button(file_frame, text="🎵 浏览", command=self.browse_file)
        self.browse_btn.grid(row=0, column=2)

        # 文件信息
        self.file_info_var = tk.StringVar(value="")
        self.file_info_label = ttk.Label(file_frame, textvariable=self.file_info_var, style='Status.TLabel')
        self.file_info_label.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

    def create_device_section(self, parent, row):
        """创建设备选择区域"""
        device_frame = ttk.LabelFrame(parent, text="🎧 音频设备配置", padding="10")
        device_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        device_frame.columnconfigure(1, weight=1)

        # 设备配对选择
        ttk.Label(device_frame, text="设备配对:", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        self.device_pair_var = tk.StringVar()
        self.device_pair_combo = ttk.Combobox(device_frame, textvariable=self.device_pair_var, state='readonly')
        self.device_pair_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.device_pair_combo.bind('<<ComboboxSelected>>', self.on_device_pair_selected)

        self.refresh_devices_btn = ttk.Button(device_frame, text="🔄 刷新", command=self.refresh_devices)
        self.refresh_devices_btn.grid(row=0, column=2)

        self.test_pair_btn = ttk.Button(device_frame, text="🧪 测试配对", command=self.test_current_pair)
        self.test_pair_btn.grid(row=0, column=3, padx=(5, 0))

        self.monitor_mic_btn = ttk.Button(device_frame, text="🎤 监控麦克风", command=self.toggle_mic_monitoring)
        self.monitor_mic_btn.grid(row=0, column=4, padx=(5, 0))

        # 设备详情显示
        details_frame = ttk.Frame(device_frame)
        details_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        details_frame.columnconfigure(1, weight=1)

        ttk.Label(details_frame, text="扬声器:", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W)
        self.speaker_info_var = tk.StringVar(value="未选择")
        ttk.Label(details_frame, textvariable=self.speaker_info_var, style='Status.TLabel').grid(row=0, column=1, sticky=tk.W)

        ttk.Label(details_frame, text="麦克风:", style='Heading.TLabel').grid(row=1, column=0, sticky=tk.W)
        self.mic_info_var = tk.StringVar(value="未选择")
        ttk.Label(details_frame, textvariable=self.mic_info_var, style='Status.TLabel').grid(row=1, column=1, sticky=tk.W)

        ttk.Label(details_frame, text="采样率:", style='Heading.TLabel').grid(row=2, column=0, sticky=tk.W)
        self.sample_rate_var = tk.StringVar(value="44100Hz")
        ttk.Label(details_frame, textvariable=self.sample_rate_var, style='Status.TLabel').grid(row=2, column=1, sticky=tk.W)

        ttk.Label(details_frame, text="麦克风活动:", style='Heading.TLabel').grid(row=3, column=0, sticky=tk.W)
        self.mic_activity_var = tk.StringVar(value="未监控")
        ttk.Label(details_frame, textvariable=self.mic_activity_var, style='Status.TLabel').grid(row=3, column=1, sticky=tk.W)

        # 设备状态指示
        self.device_status_var = tk.StringVar(value="")
        self.device_status_label = ttk.Label(device_frame, textvariable=self.device_status_var, style='Status.TLabel')
        self.device_status_label.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

    def create_control_section(self, parent, row):
        """创建播放控制区域"""
        control_frame = ttk.LabelFrame(parent, text="🎛️ 播放控制", padding="10")
        control_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        control_frame.columnconfigure(1, weight=1)

        # 音量控制
        volume_frame = ttk.Frame(control_frame)
        volume_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        volume_frame.columnconfigure(1, weight=1)

        ttk.Label(volume_frame, text="🔊 音量:", style='Heading.TLabel').grid(row=0, column=0, padx=(0, 10))

        self.volume_var = tk.DoubleVar(value=70)
        self.volume_scale = ttk.Scale(volume_frame, from_=0, to=100, variable=self.volume_var,
                                     orient=tk.HORIZONTAL, command=self.on_volume_change)
        self.volume_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        self.volume_label = ttk.Label(volume_frame, text="70%", style='Status.TLabel')
        self.volume_label.grid(row=0, column=2)

        # 循环播放控制
        loop_frame = ttk.Frame(control_frame)
        loop_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))

        self.loop_var = tk.BooleanVar(value=True)
        self.loop_check = ttk.Checkbutton(loop_frame, text="🔄 循环播放", variable=self.loop_var,
                                         command=self.on_loop_change)
        self.loop_check.grid(row=0, column=0, sticky=tk.W)

        # 播放按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=(0, 15))

        self.play_btn = ttk.Button(button_frame, text="▶️ 开始播放", command=self.toggle_playback,
                                  state='disabled', style='Accent.TButton')
        self.play_btn.grid(row=0, column=0, padx=(0, 10))

        self.pause_btn = ttk.Button(button_frame, text="⏸️ 暂停", command=self.pause_playback,
                                   state='disabled')
        self.pause_btn.grid(row=0, column=1, padx=(0, 10))

        self.stop_btn = ttk.Button(button_frame, text="⏹️ 停止", command=self.stop_playback,
                                  state='disabled')
        self.stop_btn.grid(row=0, column=2)

        # 播放进度
        progress_frame = ttk.Frame(control_frame)
        progress_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E))
        progress_frame.columnconfigure(0, weight=1)

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                           maximum=100, mode='determinate')
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        self.time_var = tk.StringVar(value="00:00 / 00:00")
        self.time_label = ttk.Label(progress_frame, textvariable=self.time_var, style='Status.TLabel')
        self.time_label.grid(row=1, column=0)

    def create_advanced_section(self, parent, row):
        """创建高级设置区域"""
        self.advanced_frame = ttk.LabelFrame(parent, text="⚙️ 高级设置", padding="10")
        self.advanced_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # 音频引擎选择
        engine_frame = ttk.Frame(self.advanced_frame)
        engine_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        engine_frame.columnconfigure(1, weight=1)

        ttk.Label(engine_frame, text="音频引擎:", style='Heading.TLabel').grid(row=0, column=0, padx=(0, 10))

        self.engine_var = tk.StringVar()
        self.engine_combo = ttk.Combobox(engine_frame, textvariable=self.engine_var, state='readonly')
        self.engine_combo.grid(row=0, column=1, sticky=(tk.W, tk.E))

        # 缓冲区设置
        buffer_frame = ttk.Frame(self.advanced_frame)
        buffer_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        buffer_frame.columnconfigure(1, weight=1)

        ttk.Label(buffer_frame, text="缓冲区大小:", style='Heading.TLabel').grid(row=0, column=0, padx=(0, 10))

        self.buffer_var = tk.StringVar(value="2048")
        self.buffer_combo = ttk.Combobox(buffer_frame, textvariable=self.buffer_var,
                                        values=["512", "1024", "2048", "4096"], state='readonly')
        self.buffer_combo.grid(row=0, column=1, sticky=(tk.W, tk.E))

    def create_status_section(self, parent, row):
        """创建状态监控区域"""
        self.status_frame = ttk.LabelFrame(parent, text="📊 系统监控", padding="10")
        self.status_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # 状态网格
        status_grid = ttk.Frame(self.status_frame)
        status_grid.grid(row=0, column=0, sticky=(tk.W, tk.E))
        status_grid.columnconfigure((0, 1, 2, 3), weight=1)

        # 音频延迟
        ttk.Label(status_grid, text="音频延迟", style='Heading.TLabel').grid(row=0, column=0)
        self.latency_var = tk.StringVar(value="0ms")
        ttk.Label(status_grid, textvariable=self.latency_var, style='Status.TLabel').grid(row=1, column=0)

        # CPU使用率
        ttk.Label(status_grid, text="CPU使用率", style='Heading.TLabel').grid(row=0, column=1)
        self.cpu_var = tk.StringVar(value="0%")
        ttk.Label(status_grid, textvariable=self.cpu_var, style='Status.TLabel').grid(row=1, column=1)

        # 内存占用
        ttk.Label(status_grid, text="内存占用", style='Heading.TLabel').grid(row=0, column=2)
        self.memory_var = tk.StringVar(value="0MB")
        ttk.Label(status_grid, textvariable=self.memory_var, style='Status.TLabel').grid(row=1, column=2)

        # 音频质量
        ttk.Label(status_grid, text="音频质量", style='Heading.TLabel').grid(row=0, column=3)
        self.quality_var = tk.StringVar(value="44.1kHz/16bit")
        ttk.Label(status_grid, textvariable=self.quality_var, style='Status.TLabel').grid(row=1, column=3)

    def create_device_details_section(self, parent, row):
        """创建设备详情区域"""
        details_frame = ttk.LabelFrame(parent, text="🔍 设备详情", padding="10")
        details_frame.grid(row=row, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        details_frame.columnconfigure(0, weight=1)
        details_frame.rowconfigure(0, weight=1)

        # 创建设备树容器
        tree_container = ttk.Frame(details_frame)
        tree_container.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        tree_container.columnconfigure(0, weight=1)
        tree_container.rowconfigure(0, weight=1)

        # 设备树形列表 - 减小高度
        self.device_tree = ttk.Treeview(tree_container, columns=('type', 'channels', 'samplerate', 'status'),
                                       show='tree headings', height=6)
        self.device_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置列 - 调整宽度
        self.device_tree.heading('#0', text='设备名称')
        self.device_tree.heading('type', text='类型')
        self.device_tree.heading('channels', text='声道')
        self.device_tree.heading('samplerate', text='采样率')
        self.device_tree.heading('status', text='状态')

        self.device_tree.column('#0', width=250, minwidth=200)
        self.device_tree.column('type', width=80, minwidth=60)
        self.device_tree.column('channels', width=60, minwidth=50)
        self.device_tree.column('samplerate', width=80, minwidth=70)
        self.device_tree.column('status', width=80, minwidth=60)

        # 滚动条
        tree_scrollbar = ttk.Scrollbar(tree_container, orient=tk.VERTICAL, command=self.device_tree.yview)
        tree_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.device_tree.configure(yscrollcommand=tree_scrollbar.set)

        # 配置样式
        self.device_tree.tag_configure('virtual', background='#e8f5e8')
        self.device_tree.tag_configure('physical', background='#f8f9fa')
        self.device_tree.tag_configure('header', background='#e3f2fd', font=('Arial', 10, 'bold'))

        # 添加展开/折叠按钮
        button_frame = ttk.Frame(details_frame)
        button_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))

        self.expand_btn = ttk.Button(button_frame, text="📂 展开全部", command=self.expand_all_devices)
        self.expand_btn.grid(row=0, column=0, padx=(0, 5))

        self.collapse_btn = ttk.Button(button_frame, text="📁 折叠全部", command=self.collapse_all_devices)
        self.collapse_btn.grid(row=0, column=1)

    def browse_file(self):
        """浏览选择MP3文件"""
        file_path = filedialog.askopenfilename(
            title="选择MP3文件",
            filetypes=[("MP3文件", "*.mp3"), ("音频文件", "*.mp3;*.wav;*.m4a"), ("所有文件", "*.*")]
        )

        if file_path:
            self.load_mp3_file(file_path)

    def load_mp3_file(self, file_path):
        """加载MP3文件"""
        try:
            # 更新文件路径显示
            self.file_path_var.set(os.path.basename(file_path))
            self.current_file = file_path

            # 获取当前选择的采样率
            current_sample_rate = self.get_current_sample_rate()

            # 使用音频处理器加载文件
            success = self.audio_processor.load_mp3_file(file_path, current_sample_rate)

            if success:
                # 获取文件信息
                file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                duration = self.audio_processor.get_duration()
                duration_str = self.format_time(duration)

                info_text = f"✅ 时长: {duration_str} | 采样率: {current_sample_rate/1000:.1f}kHz | 大小: {file_size:.2f}MB | 格式: 立体声"
                self.file_info_var.set(info_text)

                # 启用播放按钮
                self.play_btn.config(state='normal')

                # 更新时间显示
                self.time_var.set(f"00:00 / {duration_str}")

                self.update_queue.put(('log', f"MP3文件加载成功: {os.path.basename(file_path)} ({current_sample_rate}Hz)"))

        except Exception as e:
            self.file_info_var.set(f"❌ 加载失败: {str(e)}")
            self.play_btn.config(state='disabled')
            self.update_queue.put(('log', f"文件加载失败: {str(e)}"))

    def refresh_devices(self):
        """刷新音频设备"""
        try:
            # 获取所有设备
            self.devices_info = self.device_manager.get_all_devices()

            # 更新设备配对下拉框
            device_pairs = self.devices_info.get('device_pairs', [])
            if device_pairs:
                pair_names = []
                for i, pair in enumerate(device_pairs):
                    speaker_name = pair['speaker']['name'][:30] + "..." if len(pair['speaker']['name']) > 30 else pair['speaker']['name']
                    mic_name = pair['microphone']['name'][:30] + "..." if len(pair['microphone']['name']) > 30 else pair['microphone']['name']
                    recommended = "🌟" if pair['recommended'] else ""
                    pair_name = f"{recommended}配对{i+1}: {speaker_name} → {mic_name} ({pair['sample_rate']}Hz)"
                    pair_names.append(pair_name)

                self.device_pair_combo['values'] = pair_names

                # 选择推荐的配对
                recommended_index = 0
                for i, pair in enumerate(device_pairs):
                    if pair['recommended']:
                        recommended_index = i
                        break

                self.device_pair_combo.current(recommended_index)
                self.on_device_pair_selected(None)  # 更新显示

                self.device_status_var.set(f"✅ 找到 {len(device_pairs)} 个设备配对")
                self.virtual_status_var.set("✅ 已连接")
            else:
                self.device_pair_combo['values'] = []
                self.device_status_var.set("❌ 未找到Virtual Audio Driver设备配对")
                self.virtual_status_var.set("❌ 未找到")

            # 更新音频引擎状态
            engine = self.audio_processor.current_engine
            if engine:
                self.engine_status_var.set(f"✅ {engine.title()}")
                available_engines = [name.title() for name, available in self.audio_processor.engines.items() if available]
                self.engine_combo['values'] = available_engines
                if available_engines:
                    self.engine_combo.set(engine.title())
            else:
                self.engine_status_var.set("❌ 无可用引擎")

            # 更新设备详情树
            self.update_device_tree()

            self.update_queue.put(('log', f"设备刷新完成 - 找到 {len(device_pairs)} 个设备配对"))

        except Exception as e:
            self.device_status_var.set(f"❌ 刷新失败: {str(e)}")
            self.update_queue.put(('log', f"设备刷新失败: {str(e)}"))

    def update_device_tree(self):
        """更新设备树形列表"""
        # 清空现有项目
        for item in self.device_tree.get_children():
            self.device_tree.delete(item)

        # 添加虚拟设备
        virtual_speakers = self.devices_info.get('virtual_speakers', [])
        virtual_mics = self.devices_info.get('virtual_microphones', [])

        if virtual_speakers or virtual_mics:
            # 虚拟设备分组
            virtual_header = self.device_tree.insert('', 'end', text='🎯 Virtual Audio Driver 设备',
                                                    values=('', '', '', ''), tags=('header',))

            for device in virtual_speakers:
                self.device_tree.insert(virtual_header, 'end',
                                      text=f"  🔊 {device['name']}",
                                      values=('虚拟扬声器', f"{device['max_output_channels']}",
                                             f"{device['default_samplerate']:.0f}Hz", '✅ 可用'),
                                      tags=('virtual',))

            for device in virtual_mics:
                self.device_tree.insert(virtual_header, 'end',
                                      text=f"  🎤 {device['name']}",
                                      values=('虚拟麦克风', f"{device['max_input_channels']}",
                                             f"{device['default_samplerate']:.0f}Hz", '✅ 可用'),
                                      tags=('virtual',))

        # 添加物理设备
        physical_speakers = self.devices_info.get('physical_speakers', [])
        physical_mics = self.devices_info.get('physical_microphones', [])

        if physical_speakers or physical_mics:
            # 物理设备分组
            physical_header = self.device_tree.insert('', 'end', text='🔊 物理音频设备',
                                                     values=('', '', '', ''), tags=('header',))

            for device in physical_speakers[:5]:  # 只显示前5个物理设备
                self.device_tree.insert(physical_header, 'end',
                                      text=f"  🔊 {device['name']}",
                                      values=('物理扬声器', f"{device['max_output_channels']}",
                                             f"{device['default_samplerate']:.0f}Hz", '⚪ 可用'),
                                      tags=('physical',))

        # 展开所有节点
        for item in self.device_tree.get_children():
            self.device_tree.item(item, open=True)

    def toggle_playback(self):
        """切换播放状态"""
        if not self.is_playing:
            self.start_playback()
        else:
            self.pause_playback()

    def start_playback(self):
        """开始播放"""
        try:
            if not self.current_file:
                messagebox.showwarning("警告", "请先选择MP3文件!")
                return

            if not self.device_pair_combo.get():
                messagebox.showwarning("警告", "请选择设备配对!")
                return

            # 获取选中的扬声器设备ID
            device_id = self.get_current_speaker_id()
            if device_id is None:
                messagebox.showwarning("警告", "无法获取扬声器设备ID!")
                return

            # 设置音频处理器参数
            self.audio_processor.set_volume(self.volume_var.get() / 100.0)
            self.audio_processor.set_loop(self.loop_var.get())

            # 开始播放
            success = self.audio_processor.start_playback(device_id)

            if success:
                self.is_playing = True
                self.play_btn.config(text="⏸️ 暂停")
                self.pause_btn.config(state='normal')
                self.stop_btn.config(state='normal')
                self.play_status_var.set("播放中")

                # 获取当前配对信息用于日志
                current_pair = self.device_pair_combo.get()
                self.update_queue.put(('log', f"开始播放MP3到虚拟麦克风 - {current_pair}"))
            else:
                messagebox.showerror("错误", "播放启动失败!")

        except Exception as e:
            messagebox.showerror("播放错误", f"播放失败:\n{str(e)}")
            self.update_queue.put(('log', f"播放失败: {str(e)}"))

    def pause_playback(self):
        """暂停/恢复播放"""
        if self.audio_processor.is_paused:
            self.audio_processor.resume()
            self.play_btn.config(text="⏸️ 暂停")
            self.play_status_var.set("播放中")
            self.update_queue.put(('log', "恢复播放"))
        else:
            self.audio_processor.pause()
            self.play_btn.config(text="▶️ 继续")
            self.play_status_var.set("已暂停")
            self.update_queue.put(('log', "暂停播放"))

    def stop_playback(self):
        """停止播放"""
        self.audio_processor.stop()
        self.is_playing = False

        # 更新按钮状态
        self.play_btn.config(text="▶️ 开始播放")
        self.pause_btn.config(state='disabled')
        self.stop_btn.config(state='disabled')
        self.play_status_var.set("已停止")

        # 重置进度
        self.progress_var.set(0)
        if self.current_file:
            duration_str = self.format_time(self.audio_processor.get_duration())
            self.time_var.set(f"00:00 / {duration_str}")

        self.update_queue.put(('log', "停止播放"))

    def on_volume_change(self, value):
        """音量改变事件"""
        volume = float(value)
        self.volume_label.config(text=f"{int(volume)}%")
        self.audio_processor.set_volume(volume / 100.0)

    def on_loop_change(self):
        """循环播放改变事件"""
        self.audio_processor.set_loop(self.loop_var.get())

    def extract_device_id(self, device_text):
        """从设备文本中提取设备ID"""
        try:
            # 格式: "设备名称 (ID: 123)"
            import re
            match = re.search(r'ID: (\d+)', device_text)
            if match:
                return int(match.group(1))
        except:
            pass
        return None

    def format_time(self, seconds):
        """格式化时间显示"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"

    def on_device_pair_selected(self, event=None):
        """设备配对选择事件"""
        try:
            selection = self.device_pair_combo.current()
            if selection >= 0:
                device_pairs = self.devices_info.get('device_pairs', [])
                if selection < len(device_pairs):
                    pair = device_pairs[selection]

                    # 更新显示信息
                    speaker_info = f"ID {pair['speaker']['id']}: {pair['speaker']['name']}"
                    mic_info = f"ID {pair['microphone']['id']}: {pair['microphone']['name']}"
                    sample_rate_info = f"{pair['sample_rate']}Hz"

                    self.speaker_info_var.set(speaker_info)
                    self.mic_info_var.set(mic_info)
                    self.sample_rate_var.set(sample_rate_info)

                    # 如果已经加载了文件，重新加载以匹配采样率
                    if self.current_file:
                        self.load_mp3_file(self.current_file)
        except Exception as e:
            self.update_queue.put(('log', f"设备配对选择失败: {str(e)}"))

    def get_current_sample_rate(self):
        """获取当前选择的采样率"""
        try:
            selection = self.device_pair_combo.current()
            if selection >= 0:
                device_pairs = self.devices_info.get('device_pairs', [])
                if selection < len(device_pairs):
                    return device_pairs[selection]['sample_rate']
        except:
            pass
        return 44100  # 默认采样率

    def get_current_speaker_id(self):
        """获取当前选择的扬声器ID"""
        try:
            selection = self.device_pair_combo.current()
            if selection >= 0:
                device_pairs = self.devices_info.get('device_pairs', [])
                if selection < len(device_pairs):
                    return device_pairs[selection]['speaker']['id']
        except:
            pass
        return None

    def test_current_pair(self):
        """测试当前选择的设备配对"""
        try:
            selection = self.device_pair_combo.current()
            if selection < 0:
                messagebox.showwarning("警告", "请先选择一个设备配对!")
                return

            device_pairs = self.devices_info.get('device_pairs', [])
            if selection >= len(device_pairs):
                messagebox.showerror("错误", "无效的设备配对选择!")
                return

            pair = device_pairs[selection]
            speaker_id = pair['speaker']['id']
            mic_id = pair['microphone']['id']
            sample_rate = pair['sample_rate']

            # 显示测试对话框
            result = messagebox.askyesno(
                "测试设备配对",
                f"即将测试设备配对:\n\n"
                f"扬声器: {pair['speaker']['name']}\n"
                f"麦克风: {pair['microphone']['name']}\n"
                f"采样率: {sample_rate}Hz\n\n"
                f"测试将播放2秒钟的测试音频。\n"
                f"如果配对正确，您应该能在其他应用中听到虚拟麦克风的声音。\n\n"
                f"是否开始测试?"
            )

            if not result:
                return

            # 在后台线程中进行测试
            test_thread = threading.Thread(target=self._run_pair_test,
                                         args=(speaker_id, mic_id, sample_rate),
                                         daemon=True)
            test_thread.start()

        except Exception as e:
            messagebox.showerror("测试错误", f"测试失败:\n{str(e)}")

    def _run_pair_test(self, speaker_id, mic_id, sample_rate):
        """在后台运行配对测试"""
        try:
            self.update_queue.put(('log', f"开始测试设备配对 - 扬声器ID:{speaker_id}, 麦克风ID:{mic_id}"))

            # 生成测试音频
            duration = 2.0
            frequency = 440  # A4音符
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            test_audio = np.sin(2 * np.pi * frequency * t) * 0.3
            test_audio = np.column_stack([test_audio, test_audio])

            # 播放测试音频
            sd.play(test_audio, samplerate=sample_rate, device=speaker_id)
            sd.wait()

            self.update_queue.put(('log', f"测试音频播放完成 - 请检查其他应用是否能接收到虚拟麦克风音频"))

            # 在主线程中显示结果
            self.root.after(100, lambda: self._show_test_result())

        except Exception as e:
            self.update_queue.put(('log', f"配对测试失败: {str(e)}"))
            self.root.after(100, lambda: messagebox.showerror("测试失败", f"配对测试失败:\n{str(e)}"))

    def _show_test_result(self):
        """显示测试结果"""
        result = messagebox.askyesno(
            "测试完成",
            "测试音频已播放完成!\n\n"
            "请检查其他应用程序(如录音机、视频会议软件等)是否能够\n"
            "从虚拟麦克风接收到刚才播放的测试音频。\n\n"
            "您是否听到了测试音频?",
            icon='question'
        )

        if result:
            messagebox.showinfo(
                "测试成功",
                "太好了! 这个设备配对工作正常。\n\n"
                "您现在可以使用这个配对来播放MP3文件到虚拟麦克风。"
            )
            self.update_queue.put(('log', "设备配对测试成功 - 配对工作正常"))
        else:
            messagebox.showwarning(
                "测试失败",
                "看起来这个设备配对没有正常工作。\n\n"
                "建议:\n"
                "1. 尝试其他设备配对\n"
                "2. 检查Windows声音设置\n"
                "3. 重新安装Virtual Audio Driver\n"
                "4. 重启计算机"
            )
            self.update_queue.put(('log', "设备配对测试失败 - 需要检查配置"))

    def toggle_mic_monitoring(self):
        """切换麦克风监控状态"""
        if not self.is_monitoring_mic:
            self.start_mic_monitoring()
        else:
            self.stop_mic_monitoring()

    def start_mic_monitoring(self):
        """开始监控麦克风"""
        try:
            # 获取当前选择的麦克风
            selection = self.device_pair_combo.current()
            if selection < 0:
                messagebox.showwarning("警告", "请先选择一个设备配对!")
                return

            device_pairs = self.devices_info.get('device_pairs', [])
            if selection >= len(device_pairs):
                messagebox.showerror("错误", "无效的设备配对选择!")
                return

            pair = device_pairs[selection]
            mic_id = pair['microphone']['id']
            sample_rate = pair['sample_rate']

            self.is_monitoring_mic = True
            self.monitor_mic_btn.config(text="⏹️ 停止监控")

            # 启动监控线程
            self.mic_monitor_thread = threading.Thread(
                target=self._monitor_microphone,
                args=(mic_id, sample_rate),
                daemon=True
            )
            self.mic_monitor_thread.start()

            self.update_queue.put(('log', f"开始监控麦克风 - ID:{mic_id}, 采样率:{sample_rate}Hz"))

        except Exception as e:
            messagebox.showerror("监控错误", f"启动麦克风监控失败:\n{str(e)}")

    def stop_mic_monitoring(self):
        """停止监控麦克风"""
        self.is_monitoring_mic = False
        self.monitor_mic_btn.config(text="🎤 监控麦克风")
        self.mic_activity_var.set("已停止")
        self.update_queue.put(('log', "停止麦克风监控"))

    def _monitor_microphone(self, mic_id, sample_rate):
        """麦克风监控线程"""
        try:
            total_samples = 0
            active_samples = 0
            max_amplitude = 0

            def audio_callback(indata, frames, time, status):
                nonlocal total_samples, active_samples, max_amplitude

                if not self.is_monitoring_mic:
                    return

                # 获取音频数据
                audio_data = indata[:, 0] if indata.shape[1] > 0 else indata.flatten()

                # 计算统计信息
                current_max = np.max(np.abs(audio_data))
                current_rms = np.sqrt(np.mean(audio_data ** 2))

                total_samples += len(audio_data)
                if current_max > 0.001:  # 活动阈值
                    active_samples += len(audio_data)

                max_amplitude = max(max_amplitude, current_max)

                # 更新活动数据
                activity_rate = (active_samples / total_samples * 100) if total_samples > 0 else 0
                self.mic_activity_data = {
                    'max_amplitude': max_amplitude,
                    'current_amplitude': current_max,
                    'activity_rate': activity_rate,
                    'rms': current_rms
                }

            # 开始录音监控
            with sd.InputStream(device=mic_id, callback=audio_callback,
                              samplerate=sample_rate, channels=2):
                while self.is_monitoring_mic:
                    

                    # 更新GUI显示
                    data = self.mic_activity_data
                    if data['current_amplitude'] > 0.01:
                        status = f"🔊 活跃 (振幅: {data['current_amplitude']:.4f})"
                    elif data['current_amplitude'] > 0.001:
                        status = f"🔇 微弱 (振幅: {data['current_amplitude']:.4f})"
                    else:
                        status = f"⚪ 静音 (振幅: {data['current_amplitude']:.6f})"

                    self.root.after(0, lambda s=status: self.mic_activity_var.set(s))

        except Exception as e:
            self.update_queue.put(('log', f"麦克风监控失败: {str(e)}"))
            self.root.after(0, lambda: self.mic_activity_var.set(f"❌ 监控失败: {str(e)}"))

    def expand_all_devices(self):
        """展开所有设备节点"""
        for item in self.device_tree.get_children():
            self.device_tree.item(item, open=True)
            for child in self.device_tree.get_children(item):
                self.device_tree.item(child, open=True)

    def collapse_all_devices(self):
        """折叠所有设备节点"""
        for item in self.device_tree.get_children():
            self.device_tree.item(item, open=False)
            for child in self.device_tree.get_children(item):
                self.device_tree.item(child, open=False)

    def toggle_compact_mode(self):
        """切换紧凑模式"""
        self.compact_mode = not self.compact_mode

        if self.compact_mode:
            self.compact_btn.config(text="📺 标准模式")
            # 隐藏高级设置和状态监控
            self.advanced_frame.grid_remove()
            self.status_frame.grid_remove()
            # 调整设备详情高度
            self.device_tree.config(height=4)
        else:
            self.compact_btn.config(text="📱 紧凑模式")
            # 显示所有区域
            self.advanced_frame.grid()
            self.status_frame.grid()
            # 恢复设备详情高度
            self.device_tree.config(height=6)

        # 更新滚动区域
        self.canvas.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def start_update_loop(self):
        """启动GUI更新循环"""
        self.process_update_queue()
        self.update_progress()
        self.root.after(100, self.start_update_loop)  # 每100ms更新一次

    def process_update_queue(self):
        """处理更新队列"""
        try:
            while True:
                update_type, value = self.update_queue.get_nowait()

                if update_type == 'log':
                    self.log_message(value)
                elif update_type == 'progress':
                    self.progress_var.set(value)
                elif update_type == 'time':
                    self.time_var.set(value)
                elif update_type == 'status':
                    self.play_status_var.set(value)

        except queue.Empty:
            pass

    def update_progress(self):
        """更新播放进度"""
        if self.is_playing and self.audio_processor.is_playing:
            try:
                current_pos = self.audio_processor.get_position()
                total_duration = self.audio_processor.get_duration()

                if total_duration > 0:
                    progress = (current_pos / total_duration) * 100
                    self.progress_var.set(progress)

                    current_time = self.format_time(current_pos)
                    total_time = self.format_time(total_duration)
                    self.time_var.set(f"{current_time} / {total_time}")

                    # 更新系统监控（模拟数据）
                    import random
                    self.latency_var.set(f"{random.randint(10, 50)}ms")
                    self.cpu_var.set(f"{random.randint(2, 15)}%")
                    self.memory_var.set(f"{random.randint(30, 80)}MB")

            except Exception as e:
                pass

    def log_message(self, message):
        """记录日志消息（可扩展为日志窗口）"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")  # 暂时输出到控制台

    def on_closing(self):
        """窗口关闭事件"""
        if self.is_playing:
            self.stop_playback()

        # 停止麦克风监控
        if self.is_monitoring_mic:
            self.stop_mic_monitoring()

        # 等待播放线程结束
        if self.audio_processor.playback_thread and self.audio_processor.playback_thread.is_alive():
            self.audio_processor.playback_thread.join(timeout=1)

        # 等待监控线程结束
        if self.mic_monitor_thread and self.mic_monitor_thread.is_alive():
            self.mic_monitor_thread.join(timeout=1)

        self.root.destroy()

def check_dependencies():
    """检查依赖库"""
    missing_deps = []

    if not SOUNDDEVICE_AVAILABLE:
        missing_deps.append("sounddevice")
    if not PYDUB_AVAILABLE:
        missing_deps.append("pydub")

    if missing_deps:
        print("⚠️ 缺少以下依赖库:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\n安装命令:")
        print(f"pip install {' '.join(missing_deps)}")
        return False

    return True

def check_virtual_audio_driver():
    """检查Virtual Audio Driver"""
    if not SOUNDDEVICE_AVAILABLE:
        return False

    try:
        devices = sd.query_devices()
        virtual_found = False

        for device in devices:
            if 'virtual audio driver' in device['name'].lower():
                virtual_found = True
                break

        return virtual_found
    except:
        return False

def show_startup_dialog():
    """显示启动对话框"""
    root = tk.Tk()
    root.withdraw()

    # 检查Virtual Audio Driver
    if not check_virtual_audio_driver():
        result = messagebox.askyesno(
            "Virtual Audio Driver未找到",
            "未检测到Virtual Audio Driver设备。\n\n"
            "这个应用需要Virtual Audio Driver才能正常工作。\n"
            "是否继续启动应用程序？\n\n"
            "注意：没有Virtual Audio Driver，虚拟麦克风功能将无法使用。",
            icon='warning'
        )

        if not result:
            root.destroy()
            return False

    root.destroy()
    return True

def main():
    """主函数"""
    print("🎤 MP3虚拟麦克风 - 终极版 v2.0")
    print("=" * 60)

    # 检查依赖
    if not check_dependencies():
        input("按Enter键退出...")
        return

    # 显示启动对话框
    if not show_startup_dialog():
        return

    # 创建主窗口
    root = tk.Tk()

    try:
        # 设置应用图标（如果有的话）
        # root.iconbitmap('icon.ico')
        pass
    except:
        pass

    # 创建应用实例
    app = VirtualMicrophoneGUI(root)

    # 设置关闭事件
    root.protocol("WM_DELETE_WINDOW", app.on_closing)

    # 居中显示窗口
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")

    print("✅ 应用程序启动成功!")
    print("📋 使用说明:")
    print("   1. 选择MP3文件")
    print("   2. 选择虚拟扬声器设备")
    print("   3. 在Windows声音设置中将Virtual Mic Driver设为默认麦克风")
    print("   4. 开始播放")
    print("   5. 其他应用程序现在可以从虚拟麦克风接收音频")

    # 启动应用
    root.mainloop()

if __name__ == "__main__":
    main()
