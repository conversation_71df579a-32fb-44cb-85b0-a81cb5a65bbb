#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Virtual Mic Driver专门配对
"""

import sounddevice as sd
import numpy as np
import time

def test_virtual_mic_driver():
    """测试Virtual Mic Driver配对"""
    print("🔍 测试Virtual Mic Driver专门配对")
    print("=" * 80)
    
    # 根据检查结果，测试Virtual Mic Driver配对
    test_pairs = [
        # (扬声器ID, 麦克风ID, 描述, 采样率)
        (16, 17, "Speakers (Virtual Audio Driver by MTT) → Microphone Array (Virtual Mic Driver by MTT)", 48000),
        (12, 17, "扬声器 (Virtual Audio Driver by MTT WASAPI) → Microphone Array (Virtual Mic Driver by MTT)", 48000),
        (10, 17, "扬声器 (Virtual Audio Driver by MTT DirectSound) → Microphone Array (Virtual Mic Driver by MTT)", 44100),
        (4, 1, "扬声器 (Virtual Audio Driver by MT) → 麦克风阵列 (Virtual Audio Driver by)", 44100),
    ]
    
    for speaker_id, mic_id, description, sample_rate in test_pairs:
        print(f"\n🔊 测试配对: {description}")
        print(f"   扬声器ID: {speaker_id}, 麦克风ID: {mic_id}, 采样率: {sample_rate}Hz")
        
        try:
            # 生成测试音频
            duration = 2.0
            frequency = 440
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            test_audio = np.sin(2 * np.pi * frequency * t) * 0.3
            test_audio = np.column_stack([test_audio, test_audio])
            
            # 录音数据收集
            recorded_data = []
            
            def record_callback(indata, frames, time, status):
                if status:
                    print(f"      录音状态: {status}")
                recorded_data.extend(indata[:, 0])  # 左声道
            
            print("   🎤 开始录音...")
            
            # 使用指定采样率录音
            with sd.InputStream(device=mic_id, callback=record_callback, 
                              samplerate=sample_rate, channels=2):
                time.sleep(0.3)  # 等待录音稳定
                
                print("   🔊 播放测试音频...")
                sd.play(test_audio, samplerate=sample_rate, device=speaker_id)
                sd.wait()  # 等待播放完成
                
                time.sleep(0.3)  # 额外录音时间
            
            # 分析结果
            if len(recorded_data) > 0:
                recorded_array = np.array(recorded_data)
                max_amplitude = np.max(np.abs(recorded_array))
                mean_amplitude = np.mean(np.abs(recorded_data))
                
                # 计算信号强度
                signal_power = np.mean(recorded_array ** 2)
                
                print(f"   📊 录音分析:")
                print(f"      样本数量: {len(recorded_data)}")
                print(f"      最大振幅: {max_amplitude:.6f}")
                print(f"      平均振幅: {mean_amplitude:.6f}")
                print(f"      信号功率: {signal_power:.6f}")
                
                # 更严格的检测阈值
                if max_amplitude > 0.001:  # 降低阈值
                    print(f"   ✅ 成功! 检测到音频信号!")
                    print(f"   🎯 这是一个有效的配对!")
                    
                    # 保存测试音频用于验证
                    print(f"   💾 建议使用这个配对进行虚拟麦克风")
                    
                elif max_amplitude > 0.0001:
                    print(f"   ⚠️ 检测到微弱信号，可能需要调整音量")
                else:
                    print(f"   ❌ 未检测到音频信号")
            else:
                print(f"   ❌ 无录音数据")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            
            # 检查设备是否支持指定采样率
            try:
                sd.check_output_settings(device=speaker_id, samplerate=sample_rate)
                print(f"      扬声器支持 {sample_rate}Hz")
            except:
                print(f"      ❌ 扬声器不支持 {sample_rate}Hz")
                
            try:
                sd.check_input_settings(device=mic_id, samplerate=sample_rate)
                print(f"      麦克风支持 {sample_rate}Hz")
            except:
                print(f"      ❌ 麦克风不支持 {sample_rate}Hz")
        
        time.sleep(1)  # 设备间隔
    
    print("\n" + "=" * 80)
    print("🎯 测试完成!")
    print("✅ 找到有效配对后，请在主程序中使用对应的设备")
    print("💡 如果所有测试都失败，可能需要:")
    print("   1. 重新安装Virtual Audio Driver")
    print("   2. 重启计算机")
    print("   3. 检查Windows声音设置")

if __name__ == "__main__":
    try:
        test_virtual_mic_driver()
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
    
    input("\n按Enter键退出...")
