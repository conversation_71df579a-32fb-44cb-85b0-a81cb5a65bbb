#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Virtual Audio Driver状态和配置
"""

import sounddevice as sd
import subprocess
import sys

def check_virtual_driver_status():
    """检查Virtual Audio Driver状态"""
    print("🔍 检查Virtual Audio Driver状态")
    print("=" * 80)
    
    # 1. 检查设备列表
    print("📋 1. 设备列表检查:")
    devices = sd.query_devices()
    virtual_devices = []
    
    for i, device in enumerate(devices):
        if 'virtual' in device['name'].lower():
            virtual_devices.append((i, device))
            print(f"   设备 {i}: {device['name']}")
            print(f"      输入: {device['max_input_channels']}, 输出: {device['max_output_channels']}")
            print(f"      采样率: {device['default_samplerate']}")
    
    if not virtual_devices:
        print("   ❌ 未找到Virtual Audio Driver设备")
        return
    
    # 2. 检查Windows音频服务
    print(f"\n🔧 2. Windows音频服务检查:")
    try:
        result = subprocess.run([
            'powershell', '-Command', 
            'Get-Service -Name "AudioSrv", "AudioEndpointBuilder" | Select-Object Name, Status'
        ], capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("   Windows音频服务状态:")
            print(f"   {result.stdout}")
        else:
            print("   ❌ 无法检查Windows音频服务")
    except Exception as e:
        print(f"   ❌ 检查失败: {e}")
    
    # 3. 检查设备管理器中的Virtual Audio Driver
    print(f"\n🎯 3. 设备管理器检查:")
    try:
        result = subprocess.run([
            'powershell', '-Command',
            'Get-WmiObject -Class Win32_SoundDevice | Where-Object {$_.Name -like "*Virtual*"} | Select-Object Name, Status'
        ], capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0 and result.stdout.strip():
            print("   Virtual Audio Driver设备状态:")
            print(f"   {result.stdout}")
        else:
            print("   ❌ 在设备管理器中未找到Virtual Audio Driver")
    except Exception as e:
        print(f"   ❌ 检查失败: {e}")
    
    # 4. 检查默认音频设备
    print(f"\n🎤 4. 默认音频设备检查:")
    try:
        default_input = sd.default.device[0]
        default_output = sd.default.device[1]
        
        input_device = devices[default_input]
        output_device = devices[default_output]
        
        print(f"   默认输入设备: {input_device['name']}")
        print(f"   默认输出设备: {output_device['name']}")
        
        if 'virtual' in input_device['name'].lower():
            print("   ✅ 默认输入设备是Virtual Audio Driver")
        else:
            print("   ⚠️ 默认输入设备不是Virtual Audio Driver")
            
        if 'virtual' in output_device['name'].lower():
            print("   ✅ 默认输出设备是Virtual Audio Driver")
        else:
            print("   ⚠️ 默认输出设备不是Virtual Audio Driver")
            
    except Exception as e:
        print(f"   ❌ 检查失败: {e}")
    
    # 5. 建议
    print(f"\n💡 5. 问题诊断和建议:")
    print("   可能的问题原因:")
    print("   1. Virtual Audio Driver未正确安装或配置")
    print("   2. 扬声器和麦克风不是配对的设备")
    print("   3. Windows音频设置中未正确配置虚拟设备")
    print("   4. Virtual Audio Driver服务未运行")
    print("   5. 需要重启计算机以激活驱动")
    
    print(f"\n🛠️ 解决建议:")
    print("   1. 重新安装Virtual Audio Driver")
    print("   2. 重启计算机")
    print("   3. 在Windows声音设置中:")
    print("      - 将Virtual Mic Driver设为默认输入设备")
    print("      - 确保Virtual Audio Driver扬声器可用")
    print("   4. 检查设备管理器中Virtual Audio Driver状态")
    print("   5. 尝试使用不同的Virtual Audio Driver版本")

if __name__ == "__main__":
    try:
        check_virtual_driver_status()
    except Exception as e:
        print(f"❌ 检查失败: {e}")
    
    input("\n按Enter键退出...")
