#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows录音测试 - 验证虚拟麦克风是否被正确调用
"""

import sounddevice as sd
import numpy as np
import wave
import time
import os
from datetime import datetime

def list_input_devices():
    """列出所有输入设备"""
    print("🎤 可用的输入设备:")
    print("=" * 60)
    
    devices = sd.query_devices()
    input_devices = []
    
    for i, device in enumerate(devices):
        if device['max_input_channels'] > 0:
            input_devices.append((i, device))
            device_type = "🎯 虚拟" if 'virtual' in device['name'].lower() else "🎤 物理"
            print(f"   {len(input_devices)}. {device_type} ID {i}: {device['name']}")
            print(f"      声道: {device['max_input_channels']}, 采样率: {device['default_samplerate']}")
    
    return input_devices

def record_from_device(device_id, device_name, duration=10):
    """从指定设备录音"""
    print(f"\n🎙️ 开始从设备录音: {device_name}")
    print(f"   设备ID: {device_id}")
    print(f"   录音时长: {duration}秒")
    print("=" * 60)
    
    try:
        # 获取设备信息
        device_info = sd.query_devices(device_id)
        sample_rate = int(device_info['default_samplerate'])
        channels = min(device_info['max_input_channels'], 2)
        
        print(f"📊 录音参数:")
        print(f"   采样率: {sample_rate}Hz")
        print(f"   声道数: {channels}")
        
        # 录音数据存储
        recorded_data = []
        
        def audio_callback(indata, frames, time, status):
            if status:
                print(f"⚠️ 录音状态: {status}")
            
            # 计算实时音频统计
            max_amplitude = np.max(np.abs(indata))
            rms = np.sqrt(np.mean(indata ** 2))
            
            # 显示实时活动
            if max_amplitude > 0.01:
                indicator = "🔊"
            elif max_amplitude > 0.001:
                indicator = "🔇"
            else:
                indicator = "⚪"
            
            print(f"\r{indicator} 振幅: {max_amplitude:.6f} | RMS: {rms:.6f}", end="")
            
            # 保存数据
            recorded_data.append(indata.copy())
        
        print(f"\n🎤 开始录音... (录音指示: 🔊=强信号 🔇=弱信号 ⚪=静音)")
        
        with sd.InputStream(device=device_id, callback=audio_callback,
                          samplerate=sample_rate, channels=channels):
            time.sleep(duration)
        
        print(f"\n✅ 录音完成!")
        
        # 分析录音结果
        if recorded_data:
            all_data = np.concatenate(recorded_data, axis=0)
            max_amplitude = np.max(np.abs(all_data))
            mean_amplitude = np.mean(np.abs(all_data))
            rms = np.sqrt(np.mean(all_data ** 2))
            
            print(f"\n📊 录音分析:")
            print(f"   总样本数: {len(all_data)}")
            print(f"   最大振幅: {max_amplitude:.6f}")
            print(f"   平均振幅: {mean_amplitude:.6f}")
            print(f"   RMS值: {rms:.6f}")
            
            # 判断录音质量
            if max_amplitude > 0.1:
                print("✅ 录音质量: 优秀 - 检测到强音频信号")
            elif max_amplitude > 0.01:
                print("✅ 录音质量: 良好 - 检测到明显音频信号")
            elif max_amplitude > 0.001:
                print("⚠️ 录音质量: 一般 - 检测到微弱音频信号")
            else:
                print("❌ 录音质量: 差 - 几乎没有音频信号")
            
            # 保存录音文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"recording_{device_id}_{timestamp}.wav"
            
            try:
                # 归一化音频数据
                if max_amplitude > 0:
                    normalized_data = (all_data / max_amplitude * 0.8).astype(np.float32)
                else:
                    normalized_data = all_data.astype(np.float32)
                
                # 保存为WAV文件
                with wave.open(filename, 'wb') as wf:
                    wf.setnchannels(channels)
                    wf.setsampwidth(2)  # 16-bit
                    wf.setframerate(sample_rate)
                    
                    # 转换为16位整数
                    int_data = (normalized_data * 32767).astype(np.int16)
                    wf.writeframes(int_data.tobytes())
                
                print(f"💾 录音已保存: {filename}")
                
            except Exception as e:
                print(f"❌ 保存录音失败: {e}")
        else:
            print("❌ 没有录音数据")
            
    except Exception as e:
        print(f"❌ 录音失败: {e}")

def main():
    """主函数"""
    print("🎤 Windows录音测试工具")
    print("用于验证虚拟麦克风是否被正确调用")
    print("=" * 60)
    
    # 列出输入设备
    input_devices = list_input_devices()
    
    if not input_devices:
        print("❌ 未找到任何输入设备")
        return
    
    # 选择设备
    while True:
        try:
            choice = input(f"\n请选择要测试的设备 (1-{len(input_devices)}, 0=退出): ")
            
            if choice == '0':
                print("👋 退出程序")
                return
            
            device_index = int(choice) - 1
            if 0 <= device_index < len(input_devices):
                device_id, device_info = input_devices[device_index]
                break
            else:
                print("❌ 无效选择，请重新输入")
                
        except ValueError:
            print("❌ 请输入有效数字")
    
    # 选择录音时长
    while True:
        try:
            duration = input("\n录音时长 (秒, 默认10秒): ")
            if duration == "":
                duration = 10
            else:
                duration = int(duration)
            
            if duration > 0:
                break
            else:
                print("❌ 录音时长必须大于0")
                
        except ValueError:
            print("❌ 请输入有效数字")
    
    print(f"\n💡 使用说明:")
    print(f"1. 确保在Windows声音设置中选择了要测试的设备作为默认输入")
    print(f"2. 在虚拟麦克风程序中开始播放音频")
    print(f"3. 观察录音过程中的实时指示器")
    print(f"4. 录音完成后查看分析结果")
    
    input("\n按Enter键开始录音...")
    
    # 开始录音
    record_from_device(device_id, device_info['name'], duration)
    
    print(f"\n🎯 测试完成!")
    print(f"如果录音检测到音频信号，说明虚拟麦克风工作正常。")
    print(f"如果没有检测到信号，请检查:")
    print(f"1. Virtual Audio Driver是否正确安装")
    print(f"2. Windows声音设置中的默认输入设备")
    print(f"3. 虚拟麦克风程序是否正在播放音频")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
    
    input("\n按Enter键退出...")
