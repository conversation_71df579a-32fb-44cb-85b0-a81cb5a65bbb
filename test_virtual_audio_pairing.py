#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Virtual Audio Driver设备配对
"""

import sounddevice as sd
import numpy as np
import time
import threading

def test_audio_pairing():
    """测试虚拟音频设备配对"""
    print("🔍 测试Virtual Audio Driver设备配对")
    print("=" * 60)
    
    # 获取所有设备
    devices = sd.query_devices()
    
    # 找到所有虚拟设备
    virtual_speakers = []
    virtual_mics = []
    
    for i, device in enumerate(devices):
        name = device['name'].lower()
        if 'virtual' in name:
            if device['max_output_channels'] > 0:
                virtual_speakers.append((i, device['name']))
            if device['max_input_channels'] > 0:
                virtual_mics.append((i, device['name']))
    
    print("🔊 找到的虚拟扬声器:")
    for i, (device_id, name) in enumerate(virtual_speakers):
        print(f"   {i+1}. ID {device_id}: {name}")
    
    print("\n🎤 找到的虚拟麦克风:")
    for i, (device_id, name) in enumerate(virtual_mics):
        print(f"   {i+1}. ID {device_id}: {name}")
    
    if not virtual_speakers or not virtual_mics:
        print("❌ 未找到足够的虚拟设备进行测试")
        return
    
    # 生成测试音频 - 1秒的正弦波
    sample_rate = 44100
    duration = 1.0
    frequency = 440  # A4音符
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    test_audio = np.sin(2 * np.pi * frequency * t) * 0.3
    test_audio = np.column_stack([test_audio, test_audio])  # 立体声
    
    print(f"\n🎵 开始测试配对 (播放 {frequency}Hz 正弦波)")
    print("=" * 60)
    
    # 测试每个扬声器与每个麦克风的配对
    for speaker_idx, (speaker_id, speaker_name) in enumerate(virtual_speakers):
        print(f"\n🔊 测试扬声器: {speaker_name} (ID: {speaker_id})")
        
        for mic_idx, (mic_id, mic_name) in enumerate(virtual_mics):
            print(f"   🎤 配对麦克风: {mic_name} (ID: {mic_id})")
            
            try:
                # 录音准备
                recorded_audio = []
                recording = True
                
                def audio_callback(indata, frames, time, status):
                    if recording:
                        recorded_audio.extend(indata[:, 0])  # 只取左声道
                
                # 开始录音
                with sd.InputStream(device=mic_id, callback=audio_callback, 
                                  samplerate=sample_rate, channels=2):
                    time.sleep(0.1)  # 等待录音稳定
                    
                    # 播放测试音频
                    sd.play(test_audio, samplerate=sample_rate, device=speaker_id)
                    time.sleep(duration + 0.2)  # 播放时间 + 缓冲
                    
                    recording = False
                
                # 分析录音结果
                if len(recorded_audio) > 0:
                    recorded_array = np.array(recorded_audio)
                    max_amplitude = np.max(np.abs(recorded_array))
                    
                    if max_amplitude > 0.01:  # 阈值检测
                        print(f"      ✅ 检测到音频! 最大振幅: {max_amplitude:.3f}")
                        print(f"      🎯 这是一个有效的配对!")
                    else:
                        print(f"      ❌ 未检测到音频 (振幅: {max_amplitude:.3f})")
                else:
                    print(f"      ❌ 录音失败")
                    
            except Exception as e:
                print(f"      ❌ 测试失败: {e}")
            
            time.sleep(0.5)  # 设备间隔
    
    print("\n" + "=" * 60)
    print("🎯 测试完成!")
    print("✅ 标记的配对可以用于虚拟麦克风功能")
    print("❌ 未检测到音频的配对无法正常工作")

if __name__ == "__main__":
    try:
        test_audio_pairing()
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
    
    input("\n按Enter键退出...")
