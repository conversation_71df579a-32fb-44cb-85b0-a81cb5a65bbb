#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查虚拟麦克风是否被正确调用
"""

import sounddevice as sd
import numpy as np
import time
import threading
import matplotlib.pyplot as plt
from collections import deque

def monitor_virtual_mic():
    """实时监控虚拟麦克风活动"""
    print("🎤 虚拟麦克风活动监控")
    print("=" * 60)
    
    # 找到虚拟麦克风设备
    devices = sd.query_devices()
    virtual_mics = []
    
    for i, device in enumerate(devices):
        if 'virtual' in device['name'].lower() and device['max_input_channels'] > 0:
            virtual_mics.append((i, device))
    
    if not virtual_mics:
        print("❌ 未找到虚拟麦克风设备")
        return
    
    print("🎤 找到的虚拟麦克风:")
    for i, (device_id, device) in enumerate(virtual_mics):
        print(f"   {i+1}. ID {device_id}: {device['name']}")
    
    # 选择要监控的麦克风
    choice = input(f"\n请选择要监控的麦克风 (1-{len(virtual_mics)}): ")
    try:
        mic_index = int(choice) - 1
        if 0 <= mic_index < len(virtual_mics):
            device_id, device = virtual_mics[mic_index]
        else:
            print("❌ 无效选择")
            return
    except:
        print("❌ 无效输入")
        return
    
    print(f"\n🎤 开始监控: {device['name']}")
    print("=" * 60)
    
    # 音频数据缓冲区
    audio_buffer = deque(maxlen=1000)
    sample_rate = int(device['default_samplerate'])
    
    # 统计数据
    total_samples = 0
    active_samples = 0
    max_amplitude = 0
    
    def audio_callback(indata, frames, time, status):
        nonlocal total_samples, active_samples, max_amplitude
        
        if status:
            print(f"⚠️ 音频状态: {status}")
        
        # 获取左声道数据
        audio_data = indata[:, 0] if indata.shape[1] > 0 else indata.flatten()
        
        # 计算统计信息
        current_max = np.max(np.abs(audio_data))
        current_rms = np.sqrt(np.mean(audio_data ** 2))
        
        total_samples += len(audio_data)
        if current_max > 0.001:  # 活动阈值
            active_samples += len(audio_data)
        
        max_amplitude = max(max_amplitude, current_max)
        
        # 添加到缓冲区
        audio_buffer.extend(audio_data)
        
        # 实时显示
        activity_indicator = "🔊" if current_max > 0.01 else "🔇" if current_max > 0.001 else "⚪"
        print(f"\r{activity_indicator} 最大振幅: {current_max:.6f} | RMS: {current_rms:.6f} | 活动率: {(active_samples/total_samples*100):.1f}%", end="")
    
    try:
        print("📊 实时监控 (按Ctrl+C停止):")
        print("🔊 = 强信号 | 🔇 = 弱信号 | ⚪ = 静音")
        print("-" * 60)
        
        with sd.InputStream(device=device_id, callback=audio_callback, 
                          samplerate=sample_rate, channels=2):
            while True:
                time.sleep(0.1)
                
    except KeyboardInterrupt:
        print(f"\n\n📊 监控结果:")
        print(f"   总样本数: {total_samples}")
        print(f"   活动样本数: {active_samples}")
        print(f"   活动率: {(active_samples/total_samples*100):.2f}%")
        print(f"   最大振幅: {max_amplitude:.6f}")
        
        if max_amplitude > 0.01:
            print("✅ 虚拟麦克风有明显音频活动")
        elif max_amplitude > 0.001:
            print("⚠️ 虚拟麦克风有微弱音频活动")
        else:
            print("❌ 虚拟麦克风无音频活动")
            
    except Exception as e:
        print(f"\n❌ 监控失败: {e}")

def test_windows_recording():
    """测试Windows录音功能"""
    print("\n🎙️ Windows录音测试")
    print("=" * 60)
    print("请按以下步骤操作:")
    print("1. 打开Windows录音机 (Win+R 输入 ms-windows-store://pdp/?productid=9wzdncrfhwkn)")
    print("2. 或者打开任何支持录音的应用程序")
    print("3. 确保选择了Virtual Audio Driver作为输入设备")
    print("4. 开始录音")
    print("5. 在虚拟麦克风程序中播放音频")
    print("6. 检查录音应用是否能录制到声音")
    
    input("\n按Enter键继续...")

if __name__ == "__main__":
    try:
        monitor_virtual_mic()
        test_windows_recording()
    except Exception as e:
        print(f"❌ 程序出错: {e}")
    
    input("\n按Enter键退出...")
